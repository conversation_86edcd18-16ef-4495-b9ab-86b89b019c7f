<template>
  <button v-if="isDevMode" class="fixed right-[60px] top-0" @click="toTargetPage">
    到开发页面
  </button>

  <button v-if="isDevMode" class="fixed right-0 top-0" @click="refreshPage">
    刷新页面
  </button>

  <div class="box-border size-screen flex flex-col overflow-hidden bg-[#EDF4FA] p-[20px]">
    <div class="mb-[16px] flex">
      <Tabs :active-tab="activeTab" :tabs="tabs" @handle-tab="handleTabSelect" />
    </div>

    <!-- AI讲 按钮 -->
    <div
      v-if="tabs[activeTab].isFetchContentCompleted && tabs[activeTab].isShowSpeechBtn && !isAnsweringQuestion && !isHistoryMode && !isUsingMic"
      class="broadcast-btn" @click="speechByText"
    >
      <div v-if="isSpeeching" class="custom-wave-class">
        <AudioWave :bar-num="5" />
      </div>
      <img v-if="!isSpeeching" src="@/assets/broadcast-playing.png" class="size-[20px]">
      <img src="@/assets/broadcast-text.png" class="h-[15px] w-[34px]">
      <!-- <span class="bg-text">AI讲</span> -->
    </div>

    <!-- <span v-if="isConvertingText && !isSpeeching" class="loader" />
    <span v-if="isConvertingText" class="bg-text">思考中，请稍等</span> -->

    <div ref="contentContainerRef" class="h-0 grow overflow-y-auto">
      <!-- <AudioWave /> -->
      <div
        v-if="[0, 1, 2].includes(activeTab)" class="relative flex rounded-[8px]"
        style="clip-path: border-box;"
      >
        <!-- <div class="w-[20px] bg-[#FFA033]" /> -->
        <div class="w-full rounded-[24px] bg-[#D7EDFF] px-[12px] pb-[12px] pt-[18px]">
          <!-- TODO: md 内容样式最后再处理 -->
          <MdPreview v-if="mdContent" class="custom-md-previewer" :model-value="mdContent" />
          <div v-else-if="!mdContent && isShowLoadingComponent" class="h-[140px] flex items-center justify-center gap-x-[10px]">
            <!-- <span v-if="remainingOfCountdown === 0" class="loader" /> -->
            <CircularProgress>
              <span v-if="remainingOfCountdown !== 0">{{ remainingOfCountdown }}</span>
            </CircularProgress>
            <!-- <span v-else class="text-[#2E2E2E]"></span> -->
            <span class="text-[#2E2E2E]">思考中，请稍等...</span>
          </div>
        </div>
      </div>

      <div v-if="[3].includes(activeTab)" class="relative flex rounded-[8px] pt-[16px]" style="clip-path: border-box;">
        <video
          v-if="videoItem.url" :src="videoItem.url" :poster="videoItem.cover" preload="none" controls
          disablePictureInPicture controlsList="nodownload" class="w-full rounded-[8px]"
        />
      </div>

      <div class="flex flex-col gap-y-[16px] px-[6px] pt-[16px]">
        <template v-for="(item, index) of chatMessageWithTabType[activeTab]" :key="index">
          <div v-if="item.type === 'preset'">
            <div class="flex-inline rounded-[0_24px_24px_24px] bg-[#CBDCFF] p-[12px]">
              <span class="shrink-0 text-[#2E2E2E] font-medium">
                AI：
              </span>

              <div>
                <span class="text-[#2E2E2E] font-normal">{{ item.message }}</span>
              </div>
            </div>
          </div>

          <div v-if="item.type === 'student'">
            <div class="flex-inline rounded-[0_24px_24px_24px] bg-white p-[12px]">
              <span class="shrink-0 text-[#2E2E2E] font-medium">
                学生：
              </span>

              <div>
                <AudioWave v-if="!item.message" />
                <span v-else class="text-[#2E2E2E] font-normal">{{ item.message }}</span>
              </div>
            </div>
          </div>

          <div v-if="item.type === 'ai'" class="flex-inline rounded-[0_24px_24px_24px] bg-[#7083FF] p-[12px]">
            <span class="shrink-0 text-white font-medium">
              AI：
            </span>
            <div>
              <AudioWave v-if="!item.message" />
              <MdPreview v-else :model-value="item.message" class="custom-answering-preview" />
            </div>
          </div>
        </template>
      </div>
    </div>

    <div v-if="false" class="flex justify-end gap-x-[10px]">
      <div
        class="box-border h-[32px] w-[100px] flex items-center justify-center rounded-[8px] bg-[#D4E8FF] px-[5px] text-[16px] text-[#2E2E2E] font-normal"
        @click="speechByText"
      >
        <span>AI讲</span>
      </div>
      <div
        class="box-border h-[32px] w-[100px] flex items-center justify-center rounded-[8px] bg-[#D4E8FF] px-[5px] text-[16px] text-[#2E2E2E] font-normal"
      >
        <span>有疑问</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCountdown, useDebounceFn, useMutationObserver } from '@vueuse/core';
import { fetchEventData } from 'fetch-sse';
import { MdPreview } from 'md-editor-v3';
import { computed, nextTick, onMounted, ref, useTemplateRef, watch } from 'vue';
import request from '@/api/http';
import AudioWave from '@/components/AudioWave.vue';
import CircularProgress from '@/components/CircularProgress.vue';
import Tabs from '@/components/tabs.vue';

type ITabType = 'understanding_agent' | 'knowledge_point_agent' | 'solution_steps_agent';

interface FetchDataParams {
  ocr_record_id: string;
  index: number;
  token: string;
  type: ITabType;
}
// 创建一个倒计时，从 5 秒开始
const { remaining: remainingOfCountdown, start: startCountdown, reset: resetCountdown } = useCountdown(5);
const contentContainer = useTemplateRef('contentContainerRef');
const isDevMode = ref(__DEV__);
const activeTab = ref(0);
const isGenerating = ref(false);
const isHistoryMode = ref(false);
const fetchDataParams = ref<FetchDataParams>({
  ocr_record_id: '',
  index: 0,
  token: '',
  type: 'understanding_agent',
});
const abortController = ref<AbortController>(new AbortController());
const isPageVisible = ref(false);
function defaultTabsData() {
  return [
    {
      name: '知识点',
      content: '',
      disabled: () => isGenerating.value,
      isFetchContentCompleted: false,
      isFetching: false,
      type: 'knowledge_point_agent',
      isShowSpeechBtn: false,
    },
    {
      name: '解题思路',
      content: '',
      disabled: () => isGenerating.value,
      isFetchContentCompleted: false,
      isFetching: false,
      type: 'understanding_agent',
      isShowSpeechBtn: true,
    },
    {
      name: '计算过程',
      content: '',
      disabled: () => isGenerating.value,
      isFetchContentCompleted: false,
      isFetching: false,
      type: 'solution_steps_agent',
      isShowSpeechBtn: true,
    },
    // {
    //   name: '课程学习',
    //   disabled: () => isGenerating.value,
    // },
  ];
}
const tabs = ref(defaultTabsData());

/**
 * webview 太早加载会导致倒计时组件提早执行 css 动画，和 js 倒计时没法同步开始导致视觉异常。
 * TODO：应该统一封装到组件内部，而非在父类中逐一调用。
 */
const isShowLoadingComponent = ref(false);
let authrizationToken = '';
let ocrRecordId = '';
let tabTypeName = '';
let fetchKnowledgeIndex = -1;
// qa 的上下文
let recordId = '';
let isRefreshing = false;
function androidBridgeFetchKnowledge(data: {
  ocr_record_id: string;
  index: number;
  token: string;
  type: ITabType;
  fromHistory: boolean;
}) {
  isPageVisible.value = true;
  resetDefaultState();
  const urlMap = {
    knowledge_point_agent: 0,
    understanding_agent: 1,
    solution_steps_agent: 2,
  };
  const { ocr_record_id, index, token, type, fromHistory } = data;
  console.log('androidBridgeFetchKnowledge', { ocr_record_id, index, token, type, fromHistory });
  // 搜题模式下 切题的时候就刷新页面（临时方案）
  if (fetchKnowledgeIndex !== -1 && (fetchKnowledgeIndex !== index) && !fromHistory) {
    // 避免重复执行刷新页面导致卡死。
    if (isRefreshing)
      return;

    activeTab.value = urlMap[type as keyof typeof urlMap];
    tabs.value[0].content = '';
    tabs.value[1].content = '';
    tabs.value[2].content = '';
    chatMessageWithTabType.value = {};

    isRefreshing = true;
    refreshPage();
    return;
  }
  if (fromHistory) {
    isHistoryMode.value = fromHistory;
    window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
    if (fetchKnowledgeIndex !== index) {
      // TODO：切题应该把所有的数据都缓存，而不是只缓存上一次的记录。
      // 历史模式里，如果上一题有历史记录、而后面的题目没有历史记录就要重新执行 AI解析 。
      // 这里需要把缓存去掉，否则会无法发起请求。
      tabs.value = defaultTabsData();
    }
  }

  authrizationToken = token;
  getPresetPhraseList();
  ocrRecordId = ocr_record_id;
  tabTypeName = type;
  fetchKnowledgeIndex = index;
  console.log(tabs.value[urlMap[type as keyof typeof urlMap]].content);

  if (tabs.value[urlMap[type as keyof typeof urlMap]].content)
    return;
  if (tabs.value[urlMap[type as keyof typeof urlMap]].isFetching)
    return;

  // if (!tabs.value[activeTab.value].isFetchContentCompleted)
  //   return;
  tabs.value.map((item) => {
    item.isFetching = true;
  });

  // window?.AndroidInterface?.onTabSwitch?.(false);
  const originSelectedTab = urlMap[type as keyof typeof urlMap]; ;
  activeTab.value = originSelectedTab;
  fetchDataParams.value = data; // 保存请求数据
  abortController.value = new AbortController();
  try {
    isShowLoadingComponent.value = true;
    startCountdown();
    fetchEventData(`${import.meta.env.VITE_BASE_URL}/api/v1/math/lecture/${type}?ocr_record_id=${ocr_record_id}&index=${index}`, {
      headers: {
        Authorization: token,
      },
      signal: abortController.value.signal,
      onMessage: (event) => {
        if (!event || !event.data)
          return;
        try {
          const data = JSON.parse(event.data);
          if (!recordId)
            recordId = data.record_id;
          // if (__DEV__)
          //   console.log('androidBridgeFetchKnowledge', data?.content);
          switch (type) {
            case 'knowledge_point_agent':
              tabs.value[0].content += data?.content || '';
              break;
            case 'understanding_agent':
              tabs.value[1].content += data?.content || '';
              break;
            case 'solution_steps_agent':
              tabs.value[2].content += data?.content || '';
              break;
            default:
              break;
          }
        }
        catch (e) {
          console.error('Failed to parse SSE message:', e);
        }
      },
      onError: (error) => {
        tabs.value[originSelectedTab].isFetchContentCompleted = false;
        tabs.value[originSelectedTab].isFetching = false;
        if (error.name !== 'AbortError') {
          console.error('Chat error:', error);
        }
      },
      onClose: async () => {
        window?.AndroidInterface?.onReceivedAiAnalysis?.(type);
        tabs.value[originSelectedTab].isFetchContentCompleted = true;
        tabs.value[originSelectedTab].isFetching = false;
        // 当前请求结束时（可能当时并没有停留在最初的tab上），
        // 如果进入页面的第一个默认 tab（有可能在sse过程中新切换为其它tab）
        // 必须在当前tab加载完毕！才能显示对应的 麦克风。
        if (!isHistoryMode.value && originSelectedTab === activeTab.value) {
          window?.AndroidInterface?.androidBridgeShowRecordView?.('true');
        }
        // window?.AndroidInterface?.onTabSwitch?.(true);
        await window.MathJax.startup.promise;
        // 使用nextTick
        nextTick(async () => {
          await window.MathJax?.typesetPromise();
        });
        console.log('onClose');
      },
      onOpen: () => {
        console.log('onOpen');
      },
    })
      .finally(() => {
        resetCountdown(0);
      });
  }
  catch (error) {
    console.error('Chat error:', error);
  }

  // 静默调取其它tab的数据。
  const typeList: ITabType[] = ['knowledge_point_agent', 'understanding_agent', 'solution_steps_agent'];
  const restTypeList = typeList.filter(t => t !== type);
  restTypeList.forEach((restType) => {
    loadContentSilently({
      ocr_record_id,
      index,
      token,
      type: restType,
    });
  });
}

function loadContentSilently(data: {
  ocr_record_id: string;
  index: number;
  token: string;
  type: ITabType;
}) {
  const urlMap = {
    knowledge_point_agent: 0,
    understanding_agent: 1,
    solution_steps_agent: 2,
  };
  const { ocr_record_id, index, token, type } = data;
  const typeIndex = urlMap[type as keyof typeof urlMap];
  try {
    fetchEventData(`${import.meta.env.VITE_BASE_URL}/api/v1/math/lecture/${type}?ocr_record_id=${ocr_record_id}&index=${index}`, {
      headers: {
        Authorization: token,
      },
      // signal: abortController.value.signal,
      onMessage: (event) => {
        if (!event || !event.data)
          return;
        try {
          const data = JSON.parse(event.data);
          if (!recordId)
            recordId = data.record_id;
          // if (__DEV__)
          //   console.log('loadContentSilently', data?.content);
          switch (type) {
            case 'knowledge_point_agent':
              tabs.value[0].content += data?.content || '';
              break;
            case 'understanding_agent':
              tabs.value[1].content += data?.content || '';
              break;
            case 'solution_steps_agent':
              tabs.value[2].content += data?.content || '';
              break;
            default:
              break;
          }
        }
        catch (e) {
          console.error('Failed to parse SSE message:', e);
        }
      },
      onError: (error) => {
        tabs.value[typeIndex].isFetchContentCompleted = false;
        tabs.value[typeIndex].isFetching = false;
        if (error.name !== 'AbortError') {
          console.error('Chat error:', error);
        }
      },
      onClose: async () => {
        console.log('onClose');
        tabs.value[typeIndex].isFetchContentCompleted = true;
        tabs.value[typeIndex].isFetching = false;
      },
      onOpen: () => {
        console.log('onOpen');
      },
    });
  }
  catch (error) {
    console.error('Chat error:', error);
  }
}

const videoItem = ref({
  url: '',
  cover: '',
});
const videoUrlList = ref([]);
function handleTabSelect(index: number) {
  console.log(index);

  if (activeTab.value === index)
    return;

  window?.AndroidInterface?.onTabSwitch?.(true);
  console.log('onTabSwitch');
  isSpeeching.value = false;

  activeTab.value = index;
  // 课程学习不处理。
  if (index === 3) {
    if (!isHistoryMode.value) {
      window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
    }
    if (!videoUrlList.value.length) {
      request.get('/api/v1/math/config', {
        params: {
          key: 'temp_videos',
        },
        headers: {
          Authorization: authrizationToken,
        },
      })
        .then((res) => {
          console.log('随机视频', res.data);
          videoUrlList.value = res.data.data || [];
          const randomIndex = Math.floor(Math.random() * videoUrlList.value.length);
          if (videoUrlList.value[randomIndex]) {
            videoItem.value = videoUrlList.value[randomIndex];
          }
          console.log('随机 randomIndex 1', randomIndex, videoItem.value);
        });
    }
    else {
      const randomIndex = Math.floor(Math.random() * videoUrlList.value.length);
      if (videoUrlList.value[randomIndex]) {
        videoItem.value = videoUrlList.value[randomIndex];
        console.log('随机 randomIndex 2', randomIndex, videoItem.value);
      }
    }
    return;
  }
  else {
    if (!isHistoryMode.value) {
      window?.AndroidInterface?.androidBridgeShowRecordView?.('true');
    }
  }

  const typeMapping: (ITabType)[] = ['knowledge_point_agent', 'understanding_agent', 'solution_steps_agent'];

  if (isHistoryMode.value) {
    window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
  }
  else {
    if (tabs.value[activeTab.value].isFetchContentCompleted) {
      window?.AndroidInterface?.androidBridgeShowRecordView?.('true');
    }
    if (!tabs.value[index].content) {
      androidBridgeFetchKnowledge({
        ocr_record_id: ocrRecordId,
        index: fetchKnowledgeIndex,
        token: authrizationToken,
        type: typeMapping[index],
        fromHistory: isHistoryMode.value,
      });
    }
  }
}

function androidBridgeFetchKnowledgeHistory(data: {
  understanding_agent: string;
  knowledge_point_agent: string;
  solution_steps_agent: string;
  tabType: ITabType;
  token: string;
}) {
  console.log('androidBridgeFetchKnowledgeHistory', data);
  isShowLoadingComponent.value = true;
  // 历史模式下，如果只有个别题目有历史数据。将这个值复原，用作缓存上一次的记录。
  fetchKnowledgeIndex = -1;
  isPageVisible.value = true;
  authrizationToken = data.token;
  getPresetPhraseList();
  // window?.AndroidInterface?.onTabSwitch?.(false);
  window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
  isHistoryMode.value = true;
  tabs.value[0].content = data.knowledge_point_agent;
  tabs.value[0].isFetchContentCompleted = true;
  tabs.value[1].content = data.understanding_agent;
  tabs.value[1].isFetchContentCompleted = true;
  tabs.value[2].content = data.solution_steps_agent;
  tabs.value[2].isFetchContentCompleted = true;

  const urlMap = {
    knowledge_point_agent: 0,
    understanding_agent: 1,
    solution_steps_agent: 2,
  };

  activeTab.value = urlMap[data.tabType as keyof typeof urlMap];
}

const mdContent = computed(() => {
  return tabs.value[activeTab.value].content;
});

const speechTextCache: { [key in number]: string } = {};
const isSpeeching = ref(false);
const isConvertingText = ref(false);
const isSpeechConverting = ref(false);
let abortConvertingController = new AbortController();
function speechByText() {
  if (isSpeeching.value) {
    console.log('speechByText', isSpeeching.value);
    window?.AndroidInterface?.androidBridgeClearTTS?.();
    androidBridgeAudioPlayFinished();
    return;
  }
  const currentActiveTab = activeTab.value;
  // 如果有缓存就播放缓存
  if (speechTextCache[currentActiveTab]) {
    if (__DEV__)
      console.log({ cache: speechTextCache[currentActiveTab] });
    isSpeeching.value = true;
    window?.AndroidInterface?.androidBridgeBroadcastByText?.(speechTextCache[currentActiveTab]);
    window?.AndroidInterface?.androidBridgeStreamStop?.();
    window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
    // TODO: 这里还需要监听播放完毕的标志。避免用户重复点击。
    return;
  }
  if (!isConvertingText.value) {
    isConvertingText.value = true;
  }
  else {
    return;
  }
  // 整句保存在此。用作缓存
  let tempText = '';
  // 短句保存在此。用作播报
  let trunckText = '';
  const splitSymbol = ['，', '；', '。', '！'];
  // window?.AndroidInterface?.onTabSwitch?.(false);
  window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
  isSpeeching.value = true;
  isSpeechConverting.value = true;
  fetchEventData(`${import.meta.env.VITE_BASE_URL}/api/v1/math/convert`, {
    // fetchEventData(`http://192.168.8.37:8000/api/v1/math/convert`, {
    method: 'post',
    headers: {
      Authorization: authrizationToken,
    },
    data: {
      text: mdContent.value,
      summary: true,
    },
    onMessage: (e) => {
      if (!e || !e.data)
        return;

      try {
        const data = JSON.parse(e.data);
        const isFinished = data?.finish_reason === 'stop';
        const content = data.content;
        const hasSplitSymbol = content ? splitSymbol.some(symbol => content.includes(symbol)) : false;
        tempText += (content || '');
        trunckText += (content || '');
        if (hasSplitSymbol) {
          if (__DEV__)
            console.log({ trunckText });
          if (trunckText && isPageVisible.value) {
            window?.AndroidInterface?.androidBridgeBroadcastByText?.(trunckText);
          }

          trunckText = '';
        }
        if (isFinished) {
          // 最后一段话可能没有分隔符。需要把最后一个 trunckText 加入到 tempText 中。
          if (trunckText && isPageVisible.value) {
            console.log('有最后一段话', trunckText);
            if (__DEV__)
              console.log({ trunckText });
            window?.AndroidInterface?.androidBridgeBroadcastByText?.(trunckText);
            trunckText = '';
          }

          console.log('convert finished', isFinished);
          // tts 结束告诉 native 停止
          window?.AndroidInterface?.androidBridgeStreamStop?.();

          // 缓存 tempText
          speechTextCache[currentActiveTab] = tempText;
        }
      }
      catch (e) {
        console.error('Failed to parse SSE message:', e);
      }
    },
    onError: (e) => {
      console.log('speechByText error', e);

      isSpeeching.value = false;
      isConvertingText.value = false;
      window?.AndroidInterface?.androidBridgeStreamStop?.();
      // window?.AndroidInterface?.onTabSwitch?.(true);
    },
    onClose: () => {
      if (__DEV__)
        console.log('tempText', tempText);

      isConvertingText.value = false;
    },
    onOpen: () => {
    },
  })
    .finally(() => {
      isSpeechConverting.value = false;
    });
}

const chatMessageWithTabType = ref<{ [key in number]: { message: string; type: string }[] }>({});
function onReceiveAsrResult(text: string, isLoading: boolean) {
  console.log('onReceiveAsrResult', { text, isLoading });
  if (!text && isLoading) {
    const newMessage = {
      type: 'student',
      message: '',
    };
    if (!chatMessageWithTabType.value[lastClickMicInActiveTab]) {
      chatMessageWithTabType.value[lastClickMicInActiveTab] = [];
    }
    chatMessageWithTabType.value[lastClickMicInActiveTab].push(newMessage);
  }
  else if (text && !isLoading) {
    // debouncedReceiveAsrResult(text, isLoading);
    const chatMessage = chatMessageWithTabType.value[lastClickMicInActiveTab];
    chatMessage[chatMessage.length - 1].message = text;
    askQuestion(text);
  }
  else if (!text && !isLoading) {
    if (lastClickMicInActiveTab !== -1) {
      // 没说话，什么都没返回。
      const chatMessage = chatMessageWithTabType.value[lastClickMicInActiveTab];
      chatMessage.splice(chatMessage.length - 1, 1);
      isUsingMic.value = false;
      lastClickMicInActiveTab = -1;
    }
  }
};
const debouncedReceiveAsrResult = useDebounceFn((text: string, isLoading: boolean) => {
  const chatMessage = chatMessageWithTabType.value[activeTab.value];
  chatMessage[chatMessage.length - 1].message = text;
  askQuestion(text);
}, 100);

let isAskingQuestion = false;
const isAnsweringQuestion = ref(false);
const isFetchingQA = ref(false);
function askQuestion(question: string) {
  if (isAskingQuestion)
    return;
  let isTabChanged = false;
  watch(activeTab, () => {
    // 在提问问题中切换tab一次后就不应该再播报。哪怕切了回来也不应该（可能只会播一小部分）
    isTabChanged = true;
    window?.AndroidInterface?.androidBridgeClearTTS?.();
  }, { once: true });
  isAskingQuestion = true;
  const typeMapping: (ITabType)[] = ['knowledge_point_agent', 'understanding_agent', 'solution_steps_agent'];
  const tabType = typeMapping[activeTab.value];
  const newMessage = {
    type: 'ai',
    message: '',
  };
  chatMessageWithTabType.value[activeTab.value].push(newMessage);
  window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
  isAnsweringQuestion.value = true;
  isFetchingQA.value = true;
  const chatMessage = chatMessageWithTabType.value[activeTab.value];
  fetchEventData(`${import.meta.env.VITE_BASE_URL}/api/v1/math/qa2`, {
    // fetchEventData(`/customIP/api/v1/math/qa2`, {
    method: 'post',
    headers: {
      Authorization: authrizationToken,
    },
    data: {
      session_id: recordId,
      query: question,
      type: tabType,
    },
    onMessage: (e) => {
      if (!e || !e.data)
        return;

      try {
        const data = JSON.parse(e.data);
        if (__DEV__) {
          // console.log('QA 回答', data, data.content);
          console.log('QA 回答', data.content);
        }

        if (data?.session_id) {
          chatMessage[chatMessage.length - 1].message += data.content;
          // 后端生成的文本可能会出现 \\n 这个字符串导致 katex 渲染失败。
          chatMessage[chatMessage.length - 1].message = chatMessage[chatMessage.length - 1].message?.replaceAll?.('\\n', '\n');
        }
        if (data?.thread_id && isPageVisible.value && !isTabChanged) {
          window?.AndroidInterface?.androidBridgeBroadcastByText?.(data.content);
        }
      }
      catch (error) {
        console.error('Failed to parse SSE answer message:', error);
      }
    },
    onError: () => {
      chatMessage[chatMessage.length - 1].message = '网络异常，请重新尝试。';
      window?.AndroidInterface?.androidBridgeStreamStop?.();
      if (!isHistoryMode.value) {
        window?.AndroidInterface?.androidBridgeShowRecordView?.('true');
      }
    },
    onClose: () => {
      window?.AndroidInterface?.androidBridgeStreamStop?.();
    },
  })
    .finally(() => {
      isAskingQuestion = false;
      isFetchingQA.value = false;
      isUsingMic.value = false;
    });
}

function androidBridgeAudioPlayFinished() {
  console.log('androidBridgeAudioPlayFinished');
  isSpeeching.value = false;
  isAnsweringQuestion.value = false;
  if (!isHistoryMode.value && activeTab.value !== 3) {
    window?.AndroidInterface?.androidBridgeShowRecordView?.('true');
  }
}

const presetPhraseList = ref([]);
function getPresetPhraseList() {
  if (isHistoryMode.value)
    return;
  if (presetPhraseList.value.length)
    return;

  request.get('/api/v1/math/config', {
    params: {
      key: 'h5',
    },
    headers: {
      Authorization: authrizationToken,
    },
  })
    .then((res) => {
      console.log('getPresetPhraseList', res.data);
      presetPhraseList.value = res.data?.awake_prompt || [];
    });
};
/** 记录当前的 tab 下，是否已经说了一次预设的问话。 */
const presetPhraseCounter = ref<{ [ key in ITabType ]: boolean }>({
  knowledge_point_agent: false,
  understanding_agent: false,
  solution_steps_agent: false,
});
/** 当 mic 结束时，如果没有返回东西（没说话）就把用户新增的聊天记录删掉。 */
let lastClickMicInActiveTab = -1;
const isUsingMic = ref(false);
function androidBridgeOnMicClick() {
  lastClickMicInActiveTab = activeTab.value;
  const typeMapping: (ITabType)[] = ['knowledge_point_agent', 'understanding_agent', 'solution_steps_agent'];
  const currentTabName = typeMapping[activeTab.value];
  if (presetPhraseCounter.value[currentTabName]) {
    return '';
  }
  else {
    presetPhraseCounter.value[currentTabName] = true;
  }
  isUsingMic.value = true;
  const targetPhraseList = presetPhraseList.value;
  const randomIndex = Math.floor(Math.random() * targetPhraseList.length);
  const phrase = targetPhraseList[randomIndex] || '';
  const newMessage = {
    type: 'preset',
    message: phrase,
  };
  const chatMessage = chatMessageWithTabType.value[activeTab.value];
  if (chatMessage) {
    chatMessage.push(newMessage);
  }
  else {
    chatMessageWithTabType.value[activeTab.value] = [newMessage];
  }

  console.log('androidBridgeOnMicClick', phrase);
  return phrase;
}

const moveToBottom = useDebounceFn(() => {
  contentContainer.value!.scrollTo({
    top: contentContainer.value!.scrollHeight,
    behavior: 'smooth',
  });
}, 200);

function androidBridgePageClosed() {
  setTimeout(() => {
    console.log('androidBridgePageClosed');
    isPageVisible.value = false;
    window?.AndroidInterface?.androidBridgeStreamStop?.();
    window?.AndroidInterface?.androidBridgeClearTTS?.();
    androidBridgeAudioPlayFinished();
  }, 200);
}

function resetDefaultState() {
  if (isSpeechConverting.value) {
    window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
    isSpeeching.value = true;
  }
  if (isFetchingQA.value) {
    window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
    isAnsweringQuestion.value = true;
  }
}

onMounted(() => {
  window?.AndroidInterface?.androidBridgeShowRecordView?.('false');

  useMutationObserver(contentContainer.value, () => {
    moveToBottom();
  }, {
    // attributes: true,
    subtree: true,
    childList: true,
  });

  let lastPageVisibleValue = true;
  // 监听 webview 可见性。native 休眠时会触发该回调
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      console.log('webview可见');
      // if (isPageVisible.value) {
      // }
      // resetDefaultState();
      isPageVisible.value = lastPageVisibleValue;
    }
    else {
      console.log('webview不可见');
      lastPageVisibleValue = isPageVisible.value;
    }
  });
});

window.androidBridgeFetchKnowledge = androidBridgeFetchKnowledge;
window.androidBridgeFetchKnowledgeHistory = androidBridgeFetchKnowledgeHistory;
window.onReceiveAsrResult = onReceiveAsrResult;
window.androidBridgeAudioPlayFinished2 = androidBridgeAudioPlayFinished;
window.androidBridgeOnMicClick = androidBridgeOnMicClick;
window.androidBridgePageClosed = androidBridgePageClosed;
function refreshPage() {
  window.location.reload();
};
function toTargetPage() {
  window.location.href = 'http://************:5174/math/ai-explain';
}
// androidBridgeFetchKnowledge({ index: 0, ocr_record_id: 'FBCEDE43-C29C-5697-9739-B4CFABFFC147', token: 'bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsYXQiOjE3NTMxNzc0OTMsImV4cCI6MTc1Mzc4MjI5MywianRpIjoiYzk5NTVhNjctZjNjMC00NDUzLThmMmYtZDQ4YzZkZjBjM2I4IiwidHlwZSI6ImFjY2VzcyIsInN1YmplY3QiOnsiaWQiOjE0MH19.GtYKMHp53zxZ4NOOM97PMQVmEFfHT06RBdQPrlPX24U', type: 'understanding_agent' });
</script>

<style lang="scss" scoped>
.broadcast-btn {
  @apply fixed right-[30px] top-[76px] z-[100] translate-y-[-50%] flex justify-center items-center gap-x-[4px] px-[10px];
  background-image: url('@/assets/broadcast-bg.png');
  min-width: 72px;
  height: 32px;
  background-size: 100% 32px;

  .bg-text {
    @apply pr-[5px];
    font-size: 16px;
    // width: 32px;
    font-style: italic;
    font-weight: bold;
    background-image: linear-gradient(to bottom, #3e9dfb, #6356fb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.custom-wave-class {
  :deep(.wave-loader) {
    padding: 0;
    width: 20px;
    height: 13px;
    gap: 0 2px;

    span {
      flex-shrink: 0;
      width: 1px;
      height: 13px;
    }
  }
}

.custom-md-previewer {
  @apply px-[20px];

  :deep() {
    h2 {
      font-size: 18px;
    }
  }

  :deep() {
    .katex-html {
      white-space: break-spaces;
    }
    .katex-error {
      color: #000000 !important;
    }
  }
}

.loader {
  width: 12px;
  height: 12px;
  border: 3px solid #8b72ff;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.custom-answering-preview {
  color: #ffffff;

  :deep() {
    h2 {
      font-size: 18px;
    }
  }

  :deep() {
    .katex-error {
      color: #ffffff !important;
    }
  }
}

// 隐藏全屏按钮。
video::-webkit-media-controls-fullscreen-button {
  display: none;
}
</style>
