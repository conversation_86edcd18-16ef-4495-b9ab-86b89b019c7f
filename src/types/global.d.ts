// Android Bridge 接口类型定义
interface AndroidInterface {
  onReceivedOCRData: (data: string) => void;
  onReceivedAiAnalysis: (type: 'understanding_agent' | 'knowledge_point_agent' | 'solution_steps_agent') => void;
  /** 让安卓端播放指定文本 */
  androidBridgeBroadcastByText: (text: string) => void;
  /** 获取 ASR 结果。 */
  onReceiveAsrResult: (text: string, isLoading: boolean) => void;
  /**
   * 刚切换到 tab 时调用一次（不要显示麦克风）
   * 当前 tab 的数据加载完成时调用一次。（显示麦克风）
   * 告诉 native 可以调用麦克风
   */
  onTabSwitch: (isShowMic: boolean) => void;
}

// MathJax 类型定义
interface MathJaxStartup {
  promise: Promise<void>;
}

interface MathJax {
  startup: MathJaxStartup;
  typesetPromise: () => Promise<void>;
}

// 扩展 Window 接口
declare global {
  declare let __DEV__: boolean;
  declare let __APP_ENV_MODE__: string;

  interface Window {
    // Android Bridge 方法
    androidBridgeFetchData: (params: {
      token: string;
      ocr_record_id: string;
      question?: string;
      image_base64?: string;
      question_index: number;
    }) => Promise<void>;

    androidBridgeFetchDetail: (params: {
      question: string;
      ocr_record_id: string;
      question_index: number;
      token: string;
    }) => Promise<void>;

    androidBridgeFetchKnowledge: (data: {
      ocr_record_id: string;
      index: number;
      token: string;
      type: 'understanding_agent' | 'knowledge_point_agent' | 'solution_steps_agent';
    }) => void;

    androidBridgeFetchKnowledgeHistory: (data: {
      solution_idea: string;
      knowledge_points: string;
      solution_steps: string;
    }) => void;

    onReceiveAsrResult: (text: string, isLoading: boolean) => void;

    // Android 接口
    AndroidInterface: AndroidInterface;

    // MathJax
    MathJax: MathJax;
  }
}

export { };
