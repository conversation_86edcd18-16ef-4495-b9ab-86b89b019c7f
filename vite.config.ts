import process from 'node:process';
import { fileURLToPath, URL } from 'node:url';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import vue from '@vitejs/plugin-vue';
import UnoCSS from 'unocss/vite';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  return {
    build: {
      sourcemap: true,
    },
    plugins: [
      vue(),
      UnoCSS(),
      sentryVitePlugin({
        authToken: env.SENTRY_AUTH_TOKEN,
        org: 'sentry',
        project: 'h5',
        url: 'https://sentry.xiaoxingcloud.com',
        telemetry: false,
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    define: {
      __DEV__: ['dev', 'test', 'development', 'testing', 'local'].includes(mode),
      __APP_ENV_MODE__: env.APP_ENV_MODE,
    },
    server: {
      port: 5174,
      proxy: {
        '/api': {
          target: 'https://ai-dev.xiaoxingcloud.com',
          changeOrigin: true,
        },
        '/customIP': {
          target: 'http://************:8000',
          rewrite: (path) => {
            return path.replace('/customIP', '');
          },
        },
      },
    },
  };
});
