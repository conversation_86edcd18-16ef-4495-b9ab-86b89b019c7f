.infisical_auth: &infisical_auth
  - >
    export INFISICAL_TOKEN=$(infisical login --method=universal-auth
    --client-id="${INFISICAL_CLIENT_ID}"
    --client-secret="${INFISICAL_CLIENT_SECRET}"
    --domain="${INFISICAL_URL}"
    --silent --plain)

stages:
  - build
  - deploy
  - notify

variables:
  IMAGE_NAME: '${HARBOR_BASE_URL}/app/xiaoxing-${CI_PROJECT_NAME}'
  IMAGE_TAG: '${CI_COMMIT_SHORT_SHA}'
  RUNNER_TAG: default
  INFISICAL_PROJECT_ID: 8958565e-9edd-4cb9-893f-443e1356bc28
  ENV: test
workflow:
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || ($CI_COMMIT_BRANCH == "main" && $CI_PIPELINE_SOURCE == "web")
      variables:
        K8S_NAMESPACE: xiaoxingcloud-prd
        RUNNER_TAG: prd
        ENV: prod
    - if: $CI_COMMIT_BRANCH == "dev" || ($CI_COMMIT_BRANCH == "dev" && $CI_PIPELINE_SOURCE == "web")
      variables:
        K8S_NAMESPACE: xiaoxingcloud-dev
        ENV: dev
    - if: $CI_COMMIT_BRANCH == "test" || ($CI_COMMIT_BRANCH == "test" && $CI_PIPELINE_SOURCE == "web")
      variables:
        K8S_NAMESPACE: xiaoxingcloud-test
        ENV: test
    - if: $CI_COMMIT_BRANCH && $CI_PIPELINE_SOURCE == "web"
      variables:
        K8S_NAMESPACE: xiaoxingcloud-dev
        ENV: dev

build:
  stage: build
  before_script:
    - echo "${ENV} ${INFISICAL_PROJECT_ID} /${CI_PROJECT_NAME} ${INFISICAL_URL}"
    - echo "$HARBOR_PASSWORD" | docker login $HARBOR_BASE_URL -u "$HARBOR_USERNAME" --password-stdin
    - *infisical_auth
  script:
    - infisical export --env="${ENV}" --projectId="${INFISICAL_PROJECT_ID}" --path="/${CI_PROJECT_NAME}" --domain="${INFISICAL_URL}" > .env
    - echo "APP_ENV_MODE=$ENV" >> .env
    - docker build -f deploy/Dockerfile -t ${IMAGE_NAME}:${IMAGE_TAG} --build-arg mode=${ENV} .
    - docker push ${IMAGE_NAME}:${IMAGE_TAG}
    - |
      if [[ "$CI_COMMIT_BRANCH" == "main" ]]; then
        docker tag ${IMAGE_NAME}:${IMAGE_TAG} ${IMAGE_NAME}:latest
        docker push ${IMAGE_NAME}:latest
      fi
  after_script:
    - rm .env
    - docker rmi ${IMAGE_NAME}:${IMAGE_TAG} || true
    - docker rmi ${IMAGE_NAME}:latest || true
  tags:
    - $RUNNER_TAG

deploy:
  stage: deploy
  needs:
    - build
  script:
    # 设置 kustomize 目录
    - export KUSTOMIZE_FOLDER="deploy/kustomize/overlays/${ENV}"
    # 更新 kustomize 配置中的镜像标签
    - sed -i "s|<BUILD_TAG>|${IMAGE_TAG}|g" ${KUSTOMIZE_FOLDER}/kustomization.yml
    - cat ${KUSTOMIZE_FOLDER}/kustomization.yml
    # 部署至 k8s
    - kubectl apply -k ${KUSTOMIZE_FOLDER} -n ${K8S_NAMESPACE}
    - kubectl rollout status deployment/xiaoxing-${CI_PROJECT_NAME} -n ${K8S_NAMESPACE} --timeout=300s

  tags:
    - $RUNNER_TAG

.notify_template: &notify_template |
  curl -X POST -H "Content-Type: application/json" \
    -d "{
      \"msgtype\": \"markdown\",
      \"markdown\": {
        \"content\": \"### ${NOTIFY_TITLE}\n
  项目：${CI_PROJECT_NAME}\n
  分支：${CI_COMMIT_REF_NAME}\n
  提交：${CI_COMMIT_SHORT_SHA}\n
  提交者：${CI_COMMIT_AUTHOR}\n
  提交信息：${CI_COMMIT_MESSAGE}\n
  部署环境：${K8S_NAMESPACE}\n
  ${NOTIFY_TIME_LABEL}：$(date '+%Y-%m-%d %H:%M:%S')\n
  [查看流水线详情](${CI_PIPELINE_URL})\"
      }
    }" \
    "${WEBHOOK_URL}"

notify_success:
  stage: notify
  image: curlimages/curl:latest
  variables:
    NOTIFY_TITLE: 部署通知 ✅
    NOTIFY_TIME_LABEL: 部署时间
  script:
    # 发送到深圳本地告警群
    - export WEBHOOK_URL="$WECOM_WEBHOOK_URL"
    - *notify_template
    # # 发送到广州团队通知
    # - export WEBHOOK_URL="$GUANGZHOU_WECOM_WEBHOOK_URL"
    # - *notify_template
  rules:
    - if: $CI_COMMIT_BRANCH
      when: on_success

notify_failure:
  stage: notify
  variables:
    NOTIFY_TITLE: 部署失败通知 ❌
    NOTIFY_TIME_LABEL: 失败时间
  script:
    # 发送到深圳本地告警群
    - export WEBHOOK_URL="$WECOM_WEBHOOK_URL"
    - *notify_template
    # # 发送到第二个群
    # - export WEBHOOK_URL="$GUANGZHOU_WECOM_WEBHOOK_URL"
    # - *notify_template
  rules:
    - if: $CI_COMMIT_BRANCH
      when: on_failure
